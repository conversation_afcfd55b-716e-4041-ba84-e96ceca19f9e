<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
            <el-form-item label="行业" prop="industry">
                <el-select v-model="queryParams.industry" placeholder="请选择行业" clearable>
                    <el-option v-for="item in industryOptions" :key="item" :label="item" :value="item" />
                </el-select>
            </el-form-item>
            <el-form-item label="主管部门" prop="department">
                <el-select v-model="queryParams.department" placeholder="请选择主管部门" clearable>
                    <el-option v-for="item in departmentOptions" :key="item" :label="item" :value="item" />
                </el-select>
            </el-form-item>
            <el-form-item label="属地" prop="territory">
                <el-select v-model="queryParams.territory" placeholder="请选择属地" clearable>
                    <el-option v-for="item in territoryOptions" :key="item" :label="item" :value="item" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="getList">搜索</el-button>
                <el-button icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="list">
            <el-table-column label="ID" align="center" prop="id" />
            <el-table-column label="行业" align="center" prop="industry" />
            <el-table-column label="主管部门" align="center" prop="department" />
            <el-table-column label="属地" align="center" prop="territory" />
            <el-table-column label="操作" width="280" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button link type="primary" icon="Edit" @click="handleEdit(scope.row)">编辑</el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改属性对话框 -->
        <el-dialog :title="title" v-model="dialogVisible" width="500px" append-to-body @close="handleClose">
            <el-form ref="dataForm" :model="dataForm" :rules="dataRules" label-width="100px">
                <el-form-item label="行业" prop="industry">
                    <el-select v-model="dataForm.industry" placeholder="请选择行业" style="width: 100%">
                        <el-option v-for="item in industryOptions" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item label="主管部门" prop="department">
                    <el-select v-model="dataForm.department" placeholder="请选择主管部门" style="width: 100%">
                        <el-option v-for="item in departmentOptions" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-form-item>
                <el-form-item label="属地" prop="territory">
                    <el-select v-model="dataForm.territory" placeholder="请选择属地" style="width: 100%">
                        <el-option v-for="item in territoryOptions" :key="item" :label="item" :value="item" />
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="handleClose">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import { getList, addcontacts, updatecontacts, getcontacts, delcontacts } from "@/api/manage/contacts";
export default {
    data() {
        return {
            showSearch: true,
            isChange: false,
            list: [],
            loading: false,
            queryParams: {
                industry: '',
                department: '',
                territory: '',
                pageNum: 1,
                pageSize: 10
            },
            total: 0,
            dialogVisible: false,
            title: '',
            editId: null,
            dataForm: {
                industry: "",
                department: "",
                territory: ""
            },
            // 行业选项
            industryOptions: [
                '餐饮业',
                '工业',
                '建筑业',
                '交通运输、仓储和邮政业',
                '科学研究和技术服务业',
                '社零、批零',
                '文化、体育和娱乐业',
                '信息传输、软件和信息技术服务业',
                '住宿业',
                '租赁和商务服务'
            ],
            // 主管部门选项
            departmentOptions: [
                '经信局',
                '商务局',
                '住建委',
                '文旅局',
                '宣传部',
                '科委',
                '发改委',
                '首都机场临空经济区管委会'
            ],
            // 属地选项
            territoryOptions: [
                '北石槽镇',
                '北务镇',
                '北小营镇',
                '大孙各庄镇',
                '高丽营镇',
                '后沙峪镇',
                '李桥镇',
                '李遂镇',
                '龙湾屯镇',
                '马坡镇',
                '木林镇',
                '南彩镇',
                '南法信镇',
                '牛栏山镇',
                '仁和镇',
                '杨镇',
                '张镇',
                '赵全营镇',
                '天竺镇',
                '空港街道',
                '双丰街道',
                '胜利街道',
                '中关村顺义园管委会',
                '首都机场临空经济区管委会'
            ],
            dataRules: {
                industry: [
                    { required: true, message: '行业不能为空', trigger: 'change' }
                ],
                department: [
                    { required: true, message: '主管部门不能为空', trigger: 'change' }
                ],
                territory: [
                    { required: true, message: '属地不能为空', trigger: 'change' }
                ]
            }
        }
    },
    mounted() {
        this.getList()
    },
    methods: {
        submitForm() {
            this.$refs.dataForm.validate(async (valid) => {
                if (valid) {
                    let dataForm = JSON.parse(JSON.stringify(this.dataForm));

                    if (this.isChange) {
                        dataForm.id = this.editId;
                        let res = await updatecontacts(dataForm)
                        if (res.code == 200) {
                            this.$message({
                                message: '修改成功',
                                type: 'success'
                            });
                            this.getList()
                            this.dialogVisible = false
                        }
                    } else {
                        let res = await addcontacts(dataForm)
                        if (res.code == 200) {
                            this.$message({
                                message: '新增成功',
                                type: 'success'
                            });
                            this.getList()
                            this.dialogVisible = false
                        }
                    }
                } else {
                    return false;
                }
            });
        },
        handleAdd() {
            this.isChange = false;
            this.title = "新增属性";
            this.dialogVisible = true;
            this.$nextTick(() => {
                this.$refs['dataForm'].resetFields();
                this.dataForm = JSON.parse(JSON.stringify(this.$options.data().dataForm))
            })
        },
        handleClose() {
            this.$refs['dataForm'].resetFields();
            this.dataForm = JSON.parse(JSON.stringify(this.$options.data().dataForm))
            this.dialogVisible = false;
        },
        async handleEdit(val) {
            this.title = "修改属性";
            let res = await getcontacts(val.id)
            if (res.code == 200) {
                this.dataForm = res.data;
                this.editId = val.id; // 设置编辑时的ID
            }
            this.dialogVisible = true;
            this.isChange = true;
        },
        handleDelete(val) {
            this.$confirm('确认删除吗？')
                .then(async (_) => {
                    let res = await delcontacts(val.id)
                    if (res.code == 200) {
                        this.$message({
                            message: '删除成功',
                            type: 'success'
                        });
                        this.getList()
                    }
                })
        },
        reset() {
            this.queryParams = {
                industry: '',
                department: '',
                territory: '',
                pageNum: 1,
                pageSize: 10
            };
            this.getList()
        },
        async getList() {
            this.loading = true;
            let res = await getList(this.queryParams)
            if (res.code == 200) {
                this.total = res.total;
                this.list = res.rows;
                this.loading = false;
            }
        }
    },
}

</script>
<style lang="scss" scoped>
:deep(.el-dialog) {
    background: #FFFFFF;
    border-radius: 14px;
    position: relative;
}
</style>
